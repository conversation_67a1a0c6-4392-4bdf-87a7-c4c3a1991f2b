// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey:
        'AIzaSyCbOlQ85Gmz1wBVnix6nVx7Y4DqpcIgK0Y', // You'll need to get this from Firebase Console under Web configuration
    appId: '1:768043028815:web:your_web_app_id', // Replace with your web app ID
    messagingSenderId: '768043028815',
    projectId: 'uclap-e03fe',
    authDomain: 'uclap-e03fe.firebaseapp.com',
    storageBucket: 'uclap-e03fe.appspot.com',
    measurementId: 'G-NS73KE3MFR', // Only if you have Google Analytics enabled
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey:
        'AIzaSyCbOlQ85Gmz1wBVnix6nVx7Y4DqpcIgK0Y', // Get from Firebase Console iOS config
    appId: '1:768043028815:ios:ef49d9ed9fd407e67b353a',
    messagingSenderId: '768043028815',
    projectId: 'uclap-e03fe',
    databaseURL: 'https://uclap-e03fe.firebaseio.com',
    storageBucket: 'uclap-e03fe.appspot.com',
    iosBundleId: 'com.fastlaundry.customerapp',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey:
        'AIzaSyCbOlQ85Gmz1wBVnix6nVx7Y4DqpcIgK0Y', // Get from Firebase Console Android config
    appId: '1:768043028815:android:23e1fe75c2b3ec6e7b353a',
    messagingSenderId: '768043028815',
    projectId: 'uclap-e03fe',
    databaseURL: 'https://uclap-e03fe.firebaseio.com',
    storageBucket: 'uclap-e03fe.appspot.com',
  );
}
