name: laundry
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ">=2.18.4 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  get: ^4.6.5
  shared_preferences: ^2.0.20
  google_maps_flutter: ^2.2.3
  permission_handler: ^11.1.0
  geolocator: ^13.0.1
  geocoding: ^3.0.0
  cached_network_image: ^3.2.3
  dotted_line: ^3.1.0
  image_picker: ^1.0.5
  get_storage: ^2.0.3
  http: ^0.13.5
  intl_phone_field: ^3.1.0
  fluttertoast: ^8.1.2
  #  firebase_core: ^2.4.1
  firebase_core: ^3.7.0
  firebase_auth: ^5.3.2
  #  firebase_auth: ^4.2.5
  cloud_firestore: ^5.4.5
  firebase_analytics: ^11.3.4
  flutter_widget_from_html_core: any
  accordion: ^2.5.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  sqflite: ^2.2.6
  intl:
  razorpay_flutter: ^1.3.4
  badges: ^3.0.3
  #  flutter_paystack: ^1.0.7
  flutter_google_places_hoc081098: ^1.2.0
  google_maps_webservice: ^0.0.20-nullsafety.5
  google_api_headers: ^4.2.0
  #  webview_flutter: ^4.10.0
  webview_flutter: ^2.8.0
  onesignal_flutter: ^5.2.7
  #  flutter_share: ^2.0.0
  #  package_info_plus: ^4.0.2
  package_info_plus: ^4.2.0
  lottie: ^3.0.0
  flutter_rating_bar: ^4.0.1
  pin_code_fields: ^8.0.1
  carousel_slider: ^5.0.0
  table_calendar:
  readmore: ^3.0.0
  http_auth: ^1.0.1
  flutter_spinkit: ^5.1.0
  dio: ^5.3.3
  pretty_dio_logger: ^1.3.1
  flutter_svg: ^2.0.7
  share_plus: ^10.1.0
  # scroll_to_index: ^3.0.1

dev_dependencies:
  flutter_launcher_icons: ^0.13.1
flutter_icons:
  android: true
  ios: true
  image_path: "assets/logo.png"
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/
    - assets/lotties/
    - assets/bottomIcons/
    - assets/trakersIcons/

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Gilroy Black
      fonts:
        - asset: fonts/BasisGrotesqueArabicPro-Black.ttf
    - family: Gilroy Light
      fonts:
        - asset: fonts/BasisGrotesqueArabicPro-Light.ttf
    - family: Gilroy Heavy
      fonts:
        - asset: fonts/Gilroy-Heavy.otf
    - family: Gilroy Medium
      fonts:
        - asset: fonts/BasisGrotesqueArabicPro-Medium.ttf
    - family: Gilroy Bold
      fonts:
        - asset: fonts/BasisGrotesqueArabicPro-Bold.ttf
    - family: Gilroy ExtraBold
      fonts:
        - asset: fonts/Gilroy-ExtraBold.otf
    - family: Gilroy Regular
      fonts:
        - asset: fonts/BasisGrotesqueArabicPro-Regular.ttf
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
